package com.asw.middleware.repository.middleware;

import com.asw.middleware.enums.CustomerTemplateStatus;
import com.asw.middleware.model.middleware.CustomerTemplate;
import java.util.List;
import java.util.Optional;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerTemplateRepository extends JpaRepository<CustomerTemplate, String> {

    List<CustomerTemplate> findAllByStatusInAndIsPrimaryAddressTrue(List<CustomerTemplateStatus> retrying);

    Optional<CustomerTemplate> findFirstByScUserCodeOrderByCreatedOnDesc(String scUserCode);
}