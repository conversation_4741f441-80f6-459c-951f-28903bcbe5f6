package com.asw.middleware.repository.middleware;

import com.asw.middleware.enums.CustomerTemplateStatus;
import com.asw.middleware.model.middleware.CustomerTemplate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerTemplateRepository extends JpaRepository<CustomerTemplate, String> {

    List<CustomerTemplate> findAllByStatusInAndIsPrimaryAddressTrue(List<CustomerTemplateStatus> retrying);

    Optional<CustomerTemplate> findFirstByScUserCodeOrderByCreatedOnDesc(String scUserCode);

    /**
     * Find the latest CustomerTemplate for each scUserCode based on createdOn
     * Uses EXISTS subquery to find templates with maximum created_on for each scUserCode
     * This approach is more readable than window functions
     *
     * @param scUserCodes Set of scUserCodes to find latest templates for
     * @return List of latest CustomerTemplates for each scUserCode
     */
    @Query(value = """
        SELECT ct.*
        FROM customer_template ct
        WHERE ct.sc_user_code IN :scUserCodes
          AND NOT EXISTS (
              SELECT 1
              FROM customer_template ct2
              WHERE ct2.sc_user_code = ct.sc_user_code
                AND ct2.created_on > ct.created_on
          )
        """, nativeQuery = true)
    List<CustomerTemplate> findLatestByScUserCodes(@Param("scUserCodes") Set<String> scUserCodes);
}