package com.asw.middleware.repository.middleware;

import com.asw.middleware.enums.CustomerTemplateStatus;
import com.asw.middleware.model.middleware.CustomerTemplate;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface CustomerTemplateRepository extends JpaRepository<CustomerTemplate, String> {

    List<CustomerTemplate> findAllByStatusInAndIsPrimaryAddressTrue(List<CustomerTemplateStatus> retrying);

    Optional<CustomerTemplate> findFirstByScUserCodeOrderByCreatedOnDesc(String scUserCode);

    /**
     * Find the latest CustomerTemplate for each scUserCode based on createdOn
     * Uses window function to get the most recent template per scUserCode
     *
     * @param scUserCodes Set of scUserCodes to find latest templates for
     * @return List of latest CustomerTemplates for each scUserCode
     */
    @Query(value = """
        SELECT ct.*
        FROM (
            SELECT *,
                   ROW_NUMBER() OVER (PARTITION BY sc_user_code ORDER BY created_on DESC) as rn
            FROM customer_template
            WHERE sc_user_code IN :scUserCodes
        ) ct
        WHERE ct.rn = 1
        """, nativeQuery = true)
    List<CustomerTemplate> findLatestByScUserCodes(@Param("scUserCodes") Set<String> scUserCodes);
}