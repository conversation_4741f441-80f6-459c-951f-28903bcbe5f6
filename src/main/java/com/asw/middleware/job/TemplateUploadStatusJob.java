package com.asw.middleware.job;

import com.asw.middleware.enums.CustomerTemplateStatus;
import com.asw.middleware.model.middleware.CustomerTemplate;
import com.asw.middleware.repository.middleware.CustomerTemplateRepository;
import com.asw.middleware.service.TemplateService;
import java.util.ArrayList;
import java.util.List;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

@Slf4j
@Component
@RequiredArgsConstructor
public class TemplateUploadStatusJob {

    private final CustomerTemplateRepository customerTemplateRepository;
    private final TemplateService templateService;

    @Transactional
    @Scheduled(fixedDelayString = "${asw.template.check-interval:5000}") // Default 5 minutes
    public void checkUploadStatus() {
        log.info("Checking upload status for templates");

        // Get templates with DOWNLOADED, PENDING and AVAILABLE_FOR_RETRY status
        List<CustomerTemplate> templates = customerTemplateRepository.findAllByStatusInAndIsPrimaryAddressTrue(
            List.of(CustomerTemplateStatus.DOWNLOADED, CustomerTemplateStatus.PENDING, CustomerTemplateStatus.AVAILABLE_FOR_RETRY));

        if (templates.isEmpty()) {
            log.info("No templates to check upload status");
            return;
        }

        log.info("Checking upload status for {} templates", templates.size());
        List<CustomerTemplate> templatesToSave = new ArrayList<>();

        for (CustomerTemplate template : templates) {
            try {
                if (templateService.checkAndUpdateTemplateStatus(template)) {
                    templatesToSave.add(template);
                }
            } catch (Exception e) {
                log.error("Error checking template status for account {}: {}", template.getScAccountNo(), e.getMessage());
            }
        }

        if (!templatesToSave.isEmpty()) {
            
            customerTemplateRepository.saveAll(templatesToSave);
        }
    }
} 